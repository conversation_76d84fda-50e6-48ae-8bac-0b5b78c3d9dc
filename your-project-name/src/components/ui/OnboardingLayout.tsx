"use client";

import { ReactNode } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface OnboardingLayoutProps {
  activeStep: 1 | 2 | 3;
  children: ReactNode;
}

export default function OnboardingLayout({
  activeStep,
  children,
}: OnboardingLayoutProps) {
  const steps = ["Sign Up", "Verification", "Personalization"];

  return (
    <div className="relative flex min-h-screen items-center justify-center bg-[url('/images/bg.jpg')] bg-cover py-10">
      {/* Glass card container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
        className="relative w-full max-w-2xl overflow-hidden rounded-3xl shadow-2xl backdrop-blur-xl"
      >
        <div className="absolute inset-0 bg-white/10 backdrop-blur-2xl" />
        <div className="absolute inset-0 rounded-3xl border border-white/15" />

        <Card className="relative z-20 flex flex-col bg-transparent px-8 py-10 text-white">
          {/* Header: Logo & Title */}
          <header className="flex flex-col items-center text-center">
            <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-white/10 backdrop-blur">
              <Image src="/images/logo.png" alt="ቀለሜ logo" width={48} height={48} />
            </div>
            <h1 className="mb-2 text-4xl font-semibold">Welcome to ቀለሜ</h1>
            <p className="text-sm text-white/80">
              Let’s get you set up in just a few simple steps.
            </p>
          </header>

          {/* Stepper */}
          <ol className="my-8 flex items-center justify-center gap-3 text-xs font-medium">
            {steps.map((label, i) => (
              <li key={label} className="flex items-center gap-2">
                <span
                  className={cn(
                    "flex h-8 w-8 items-center justify-center rounded-lg backdrop-blur text-white",
                    activeStep === i + 1 ? "bg-white/30" : "bg-white/10 text-white/60"
                  )}
                >
                  {i + 1}
                </span>
                <span className={activeStep === i + 1 ? "text-white" : "text-white/60"}>
                  {label}
                </span>
                {i < steps.length - 1 && <span className="mx-2 h-px w-6 bg-white/30" />}
              </li>
            ))}
          </ol>

          {/* Page-specific content */}
          {children}
        </Card>
      </motion.div>
    </div>
  );
}