"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import OnboardingLayout from "/Users/<USER>/Desktop/ui tester /your-project-name/src/components/ui/OnboardingLayout";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export default function PersonalizationPage() {
  const router = useRouter();
  const [form, setForm] = useState({ username: "", grade: "" });
  const isValid = form.username.trim() && form.grade.trim();

  return (
    <OnboardingLayout
      activeStep={3}
      onBack={() => router.back()}
      onNext={() => router.push("/dashboard")}
      onNextLabel="Finish"
      disableNext={!isValid}
    >
      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor="username" className="mb-2 block">
            Username
          </Label>
          <Input
            id="username"
            placeholder="cool_user123"
            value={form.username}
            onChange={(e) =>
              setForm({ ...form, username: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="grade" className="mb-2 block">
            Grade / Class
          </Label>
          <Input
            id="grade"
            placeholder="e.g. Grade 10"
            value={form.grade}
            onChange={(e) =>
              setForm({ ...form, grade: e.target.value })
            }
          />
        </div>
      </div>
    </OnboardingLayout>
  );
}