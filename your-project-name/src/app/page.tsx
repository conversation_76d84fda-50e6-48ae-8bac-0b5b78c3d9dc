"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";

/**
 * ቀለሜ – Multi-step onboarding (Sign-up → Verification → Personalization)
 */
export default function KaleMeOnboarding() {
  const [step, setStep] = useState<1 | 2 | 3>(1);
  const router = useRouter();

  const [form, setForm] = useState({
    // step 1
    fullName: "",
    contact: "",
    password: "",
    confirmPassword: "",
    terms: false,
    // step 2
    otp: "",
    // step 3
    username: "",
    grade: "",
  });

  const validators = {
    1: () =>
      form.fullName.trim() &&
      form.contact.trim() &&
      form.password.length >= 8 &&
      form.password === form.confirmPassword &&
      form.terms,
    2: () => form.otp.length === 6,
    3: () => form.username.trim() && form.grade.trim(),
  } as const;

  const onNext = () => {
    if (!validators[step]()) return;
    if (step < 3) setStep((s) => (s + 1) as 2 | 3);
    else router.push("/dashboard");
  };

  const onBack = () => step > 1 && setStep((s) => (s - 1) as 1 | 2);

  const TitleBlock = (title: string, subtitle: string) => (
    <div className="mb-6">
      <h2 className="text-2xl font-medium">{title}</h2>
      <p className="text-sm text-white/70">{subtitle}</p>
    </div>
  );

  return (
    <div className="relative flex min-h-screen items-center justify-center bg-[url('https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=2160&q=80')] bg-cover py-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
        className="relative w-full max-w-2xl overflow-hidden rounded-3xl shadow-2xl backdrop-blur-xl"
      >
        <div className="absolute inset-0 z-0 bg-white/10 backdrop-blur-2xl" />
        <div className="absolute inset-0 z-10 rounded-3xl border border-white/15" />

        <Card className="relative z-20 flex flex-col bg-transparent px-8 py-10 text-white">
          {/* Logo + heading */}
          <header className="flex flex-col items-center text-center">
            <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-white/10 backdrop-blur">
              {/* Place logo.png in public/images/logo.png */}
              <Image src="/images/logo.png" alt="ቀለሜ logo" width={48} height={48} />
            </div>
            <h1 className="mb-2 text-4xl font-semibold leading-tight">Welcome to ቀለሜ</h1>
            <p className="max-w-md text-sm text-white/80">
              Let’s get you set up in just a few simple steps.
            </p>
          </header>

          {/* Stepper */}
          <ol className="my-8 flex items-center justify-center gap-3 text-xs font-medium">
            {["Sign Up", "Verification", "Personalization"].map((label, i) => (
              <li key={label} className="flex items-center gap-2">
                <span
                  className={cn(
                    "flex h-8 w-8 items-center justify-center rounded-lg backdrop-blur text-white",
                    step === i + 1 ? "bg-white/30" : "bg-white/10 text-white/60"
                  )}
                >
                  {i + 1}
                </span>
                <span className={step === i + 1 ? "text-white" : "text-white/60"}>
                  {label}
                </span>
                {i < 2 && <span className="mx-2 h-px w-6 bg-white/30" />}
              </li>
            ))}
          </ol>

          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -40 }}
                transition={{ duration: 0.4 }}
              >
                {TitleBlock(
                  "Tell us about yourself",
                  "Step 1 of 3 • This helps us personalize your experience"
                )}

                <form className="grid grid-cols-1 gap-4" onSubmit={(e) => e.preventDefault()}>
                  {/* Full name */}
                  <div>
                    <Label htmlFor="fullName" className="mb-2 block">
                      Full name
                    </Label>
                    <Input
                      id="fullName"
                      placeholder="John Doe"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.fullName}
                      onChange={(e) => setForm({ ...form, fullName: e.target.value })}
                    />
                  </div>

                  {/* Email or Phone */}
                  <div>
                    <Label htmlFor="contact" className="mb-2 block">
                      Email or phone number
                    </Label>
                    <Input
                      id="contact"
                      placeholder="<EMAIL> or 09********"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.contact}
                      onChange={(e) => setForm({ ...form, contact: e.target.value })}
                    />
                  </div>

                  {/* Password */}
                  <div>
                    <Label htmlFor="password" className="mb-2 block">
                      Create password
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Minimum 8 characters"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.password}
                      onChange={(e) => setForm({ ...form, password: e.target.value })}
                    />
                  </div>

                  {/* Confirm Password */}
                  <div>
                    <Label htmlFor="confirmPassword" className="mb-2 block">
                      Confirm password
                    </Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Re-enter your password"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.confirmPassword}
                      onChange={(e) => setForm({ ...form, confirmPassword: e.target.value })}
                    />
                  </div>

                  {/* Terms Checkbox */}
                  <div className="mt-2 flex items-start gap-3">
                    <Checkbox
                      id="terms"
                      checked={form.terms}
                      onCheckedChange={(checked) => setForm({ ...form, terms: checked as boolean })}
                    />
                    <label htmlFor="terms" className="text-sm text-white/70">
                      I agree to the
                      <a href="/terms" target="_blank" className="mx-1 font-medium text-white hover:opacity-80">
                        Terms of Service
                      </a> and
                      <a href="/privacy" target="_blank" className="ml-1 font-medium text-white hover:opacity-80">
                        Privacy Policy
                      </a>
                    </label>
                  </div>
                </form>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -40 }}
                transition={{ duration: 0.4 }}
              >
                {TitleBlock(
                  "Verification",
                  "Step 2 of 3 • Enter the 6-digit code we sent you"
                )}
                <form className="flex flex-col items-center gap-6" onSubmit={(e) => e.preventDefault()}>
                  <Input
                    id="otp"
                    inputMode="numeric"
                    pattern="\\d{6}"
                    placeholder="••••••"
                    className="w-48 text-center tracking-widest bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                    maxLength={6}
                    value={form.otp}
                    onChange={(e) => setForm({ ...form, otp: e.target.value.replace(/[^0-9]/g, "") })}
                  />
                  <p className="text-xs text-white/60">
                    Didn’t receive it? <button type="button" className="underline">Resend</button>
                  </p>
                </form>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -40 }}
                transition={{ duration: 0.4 }}
              >
                {TitleBlock(
                  "Personalization",
                  "Step 3 of 3 • Help us tailor ቀለሜ for you"
                )}
                <form className="grid grid-cols-1 gap-4" onSubmit={(e) => e.preventDefault()}>
                  <div>
                    <Label htmlFor="username" className="mb-2 block">
                      Username
                    </Label>
                    <Input
                      id="username"
                      placeholder="cool_user123"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.username}
                      onChange={(e) => setForm({ ...form, username: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="grade" className="mb-2 block">
                      Grade / Class
                    </Label>
                    <Input
                      id="grade"
                      placeholder="e.g. Grade 10"
                      className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 text-white placeholder-gray-300 border-none focus:ring-0"
                      value={form.grade}
                      onChange={(e) => setForm({ ...form, grade: e.target.value })}
                    />
                  </div>
                </form>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation buttons */}
          <div className="mt-8 flex justify-between gap-3">
            {step > 1 ? (
              <Button variant="secondary" onClick={onBack} className="flex-1">
                Back
              </Button>
            ) : (
              <span className="flex-1" />
            )}
            <Button
              onClick={onNext}
              disabled={!validators[step]()}
              className="flex-1 font-semibold"
            >
              {step < 3 ? `Continue to Step ${step + 1}` : "Finish"}
            </Button>
          </div>
        </Card>
      </motion.div>
    </div>
  );
}