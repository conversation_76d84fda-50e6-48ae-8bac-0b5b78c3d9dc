'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
// Make sure the path is correct; adjust if needed:
// import OnboardingLayout from '../../components/OnboardingLayout';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';

export default function SignUpPage() {
  const router = useRouter();
  const [form, setForm] = useState({
    fullName: '',
    contact: '',
    password: '',
    confirm: '',
    terms: false,
  });

  const valid =
    form.fullName &&
    form.contact &&
    form.password.length >= 8 &&
    form.password === form.confirm &&
    form.terms;

  return (
    <OnboardingLayout activeStep={1}>
      {/* ---- STEP-1 FORM ---- */}
      <div className="grid gap-4">
        <Field
          id="fn"
          label="Full name"
          value={form.fullName}
          onChange={(v) => setForm({ ...form, fullName: v })}
        />
        <Field
          id="ct"
          label="Email or phone"
          value={form.contact}
          onChange={(v) => setForm({ ...form, contact: v })}
        />
        <Field
          id="pw"
          label="Create password"
          type="password"
          value={form.password}
          onChange={(v) => setForm({ ...form, password: v })}
        />
        <Field
          id="cpw"
          label="Confirm password"
          type="password"
          value={form.confirm}
          onChange={(v) => setForm({ ...form, confirm: v })}
        />

        {/* Terms */}
        <div className="mt-2 flex items-start gap-3">
          <Checkbox
            id="terms"
            checked={form.terms}
            onCheckedChange={(c) =>
              setForm({ ...form, terms: c as boolean })
            }
          />
          <label htmlFor="terms" className="text-sm text-white/70">
            I agree to the{' '}
            <a className="underline" href="/terms">
              Terms
            </a>{' '}
            and{' '}
            <a className="underline" href="/privacy">
              Privacy Policy
            </a>
          </label>
        </div>
      </div>

      {/* ---- SINGLE CENTRED CTA ---- */}
      <div className="mt-10 flex justify-center">
        <Button
          disabled={!valid}
          onClick={() => router.push('/verification')}
          className="block w-3/4 max-w-md py-4 text-lg font-semibold"
        >
          Continue to Step 2
        </Button>
      </div>
    </OnboardingLayout>
  );
}

/* Simple input helper */
function Field({
  id,
  label,
  value,
  onChange,
  type = 'text',
}: {
  id: string;
  label: string;
  value: string;
  onChange: (v: string) => void;
  type?: string;
}) {
  return (
    <div>
      <Label htmlFor={id} className="mb-2 block">
        {label}
      </Label>
      <Input
        id={id}
        type={type}
        placeholder={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
}