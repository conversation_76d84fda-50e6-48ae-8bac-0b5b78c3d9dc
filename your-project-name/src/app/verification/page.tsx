"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import OnboardingLayout from "/Users/<USER>/Desktop/ui tester /your-project-name/src/components/ui/OnboardingLayout";
import { Input } from "@/components/ui/input";

export default function VerificationPage() {
  const router = useRouter();
  const [otp, setOtp] = useState("");

  return (
    <OnboardingLayout
      activeStep={2}
      onBack={() => router.back()}
      onNext={() => router.push("/personalization")}
      onNextLabel="Continue to Step 3"
      disableNext={otp.length !== 6}
    >
      <div className="flex flex-col items-center gap-6">
        <Input
          id="otp"
          inputMode="numeric"
          pattern="\d{6}"
          placeholder="••••••"
          className="w-48 text-center tracking-widest"
          maxLength={6}
          value={otp}
          onChange={(e) =>
            setOtp(e.target.value.replace(/[^0-9]/g, ""))
          }
        />
        <p className="text-xs text-white/60">
          Didn’t receive it?{" "}
          <button type="button" className="underline">
            Resend
          </button>
        </p>
      </div>
    </OnboardingLayout>
  );
}